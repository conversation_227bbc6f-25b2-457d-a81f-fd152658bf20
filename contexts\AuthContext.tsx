import { getCurrentSession, getCurrentUser, supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import { router, usePathname } from "expo-router";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  supabaseUser: any;
  refreshUser: () => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  supabaseUser: null,
  refreshUser: async () => {},
  signOut: async () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [supabaseUser, setSupabaseUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  // Define syncUserWithSupabase first since it's used by other functions
  const syncUserWithSupabase = useCallback(async (authUser: User) => {
    if (!authUser) return;

    try {
      // Check if user exists in Supabase
      const { data: existingUser, error: fetchError } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single();

      if (fetchError && fetchError.code !== "PGRST116") {
        console.error("Error fetching user:", fetchError);
        return;
      }

      if (!existingUser) {
        // Create new user in Supabase
        const { data: newUser, error: insertError } = await supabase
          .from("users")
          .insert({
            id: authUser.id,
            email: authUser.email || "",
            full_name: authUser.user_metadata?.full_name || "",
            subscription_tier: "free",
          })
          .select()
          .single();

        if (insertError) {
          console.error("Error creating user:", insertError);
          return;
        }

        setSupabaseUser(newUser);
      } else {
        // Update existing user if needed
        const { data: updatedUser, error: updateError } = await supabase
          .from("users")
          .update({
            email: authUser.email || "",
            full_name: authUser.user_metadata?.full_name || "",
          })
          .eq("id", authUser.id)
          .select()
          .single();

        if (updateError) {
          console.error("Error updating user:", updateError);
          return;
        }

        setSupabaseUser(updatedUser);
      }
    } catch (error) {
      console.error("Error syncing user with Supabase:", error);
    }
  }, []);

  const checkSession = useCallback(async () => {
    try {
      const { session: currentSession, error } = await getCurrentSession();

      if (error) {
        throw error;
      }

      if (currentSession) {
        setSession(currentSession);
        setUser(currentSession.user);
        await syncUserWithSupabase(currentSession.user);
      } else {
        // No active session, redirect to sign in
        router.replace("/(auth)/sign-in");
      }
    } catch (error) {
      console.error("Error checking session:", error);
      router.replace("/(auth)/sign-in");
    } finally {
      setIsLoading(false);
    }
  }, [syncUserWithSupabase]);

  const refreshUser = useCallback(async () => {
    try {
      const { user: currentUser, error } = await getCurrentUser();

      if (error) {
        throw error;
      }

      if (currentUser) {
        setUser(currentUser);
        await syncUserWithSupabase(currentUser);
      }
    } catch (error) {
      console.error("Error refreshing user:", error);
    }
  }, [syncUserWithSupabase]);

  const handleSignOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      setUser(null);
      setSession(null);
      setSupabaseUser(null);
      router.replace("/(auth)/sign-in");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  }, []);

  useEffect(() => {
    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      setSession(currentSession);
      setUser(currentSession?.user || null);

      if (currentSession?.user) {
        await syncUserWithSupabase(currentSession.user);
      } else if (event === "SIGNED_OUT") {
        setSupabaseUser(null);
        // Only redirect if we're not already on the sign-in page
        if (pathname && !pathname.includes("/(auth)")) {
          router.replace("/(auth)/sign-in");
        }
      }

      setIsLoading(false);
    });

    // Initial session check
    checkSession();

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [checkSession, syncUserWithSupabase, pathname]);

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        isLoading,
        supabaseUser,
        refreshUser,
        signOut: handleSignOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
