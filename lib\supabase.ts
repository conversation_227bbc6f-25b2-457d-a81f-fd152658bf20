import { createClient } from "@supabase/supabase-js";
import { createStorageAdapter } from "./storage";
import "react-native-url-polyfill/auto";

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: createStorageAdapter(),
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Helper functions for authentication
export const signUpWithEmail = async (
  email: string,
  password: string,
  fullName: string
) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });

  return { data, error };
};

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data, error } = await supabase.auth.getUser();
  return { user: data.user, error };
};

export const getCurrentSession = async () => {
  const { data, error } = await supabase.auth.getSession();
  return { session: data.session, error };
};

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          clerk_user_id: string;
          email: string;
          full_name: string | null;
          subscription_tier: "free" | "premium" | "pro";
          subscription_expires_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          clerk_user_id: string;
          email: string;
          full_name?: string | null;
          subscription_tier?: "free" | "premium" | "pro";
          subscription_expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          clerk_user_id?: string;
          email?: string;
          full_name?: string | null;
          subscription_tier?: "free" | "premium" | "pro";
          subscription_expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      courses: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          syllabus_content: string;
          difficulty_level: "beginner" | "intermediate" | "advanced";
          estimated_duration_weeks: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          description?: string | null;
          syllabus_content: string;
          difficulty_level?: "beginner" | "intermediate" | "advanced";
          estimated_duration_weeks?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          description?: string | null;
          syllabus_content?: string;
          difficulty_level?: "beginner" | "intermediate" | "advanced";
          estimated_duration_weeks?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      study_plans: {
        Row: {
          id: string;
          course_id: string;
          user_id: string;
          title: string;
          description: string | null;
          milestones: any; // JSON array of milestones
          start_date: string;
          target_completion_date: string;
          status: "active" | "completed" | "paused";
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          course_id: string;
          user_id: string;
          title: string;
          description?: string | null;
          milestones?: any;
          start_date: string;
          target_completion_date: string;
          status?: "active" | "completed" | "paused";
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          course_id?: string;
          user_id?: string;
          title?: string;
          description?: string | null;
          milestones?: any;
          start_date?: string;
          target_completion_date?: string;
          status?: "active" | "completed" | "paused";
          created_at?: string;
          updated_at?: string;
        };
      };
      flashcards: {
        Row: {
          id: string;
          course_id: string;
          user_id: string;
          front: string;
          back: string;
          category: string | null;
          difficulty: "easy" | "medium" | "hard";
          next_review_date: string;
          review_count: number;
          ease_factor: number;
          interval_days: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          course_id: string;
          user_id: string;
          front: string;
          back: string;
          category?: string | null;
          difficulty?: "easy" | "medium" | "hard";
          next_review_date?: string;
          review_count?: number;
          ease_factor?: number;
          interval_days?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          course_id?: string;
          user_id?: string;
          front?: string;
          back?: string;
          category?: string | null;
          difficulty?: "easy" | "medium" | "hard";
          next_review_date?: string;
          review_count?: number;
          ease_factor?: number;
          interval_days?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      study_sessions: {
        Row: {
          id: string;
          user_id: string;
          course_id: string | null;
          flashcard_id: string | null;
          session_type: "flashcard_review" | "study_plan" | "general";
          duration_minutes: number;
          completed_at: string;
          performance_score: number | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          course_id?: string | null;
          flashcard_id?: string | null;
          session_type: "flashcard_review" | "study_plan" | "general";
          duration_minutes: number;
          completed_at: string;
          performance_score?: number | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          course_id?: string | null;
          flashcard_id?: string | null;
          session_type?: "flashcard_review" | "study_plan" | "general";
          duration_minutes?: number;
          completed_at?: string;
          performance_score?: number | null;
          created_at?: string;
        };
      };
    };
  };
}
